# PhotonRender - Technical Guide
**Versione**: 3.0.0-alpha | **Aggiornato**: 2025-06-20 | **Stato**: Phase 3.1 Complete

## 🎯 **Overview**

PhotonRender è un motore di rendering GPU-accelerated per SketchUp che utilizza OptiX 9.0 per hardware ray tracing. Questo documento fornisce la guida tecnica completa per sviluppo, build e deployment.

## 🏗️ **Architecture Overview**

### **Core Components**
- **C++ Rendering Engine**: High-performance ray tracing core
- **CUDA/OptiX Integration**: GPU acceleration with RTX hardware
- **Ruby-C++ Bridge**: SketchUp plugin interface
- **SketchUp Plugin**: User interface and geometry export

### **Performance Achievements**
- **CPU Baseline**: 524 Mrays/sec (Embree 4.3)
- **GPU CUDA**: 3,521 Mrays/sec (167.9x speedup)
- **OptiX Target**: 10+ Grays/sec (RTX 4070 + 36 RT Cores)
- **Memory Efficiency**: 100% hit rate, zero leaks

## 🛠️ **Development Environment**

### **Requirements**
- **OS**: Windows 10/11 (64-bit)
- **IDE**: Visual Studio 2022 Community/Professional
- **GPU**: NVIDIA RTX 20/30/40 series (RTX 4070+ recommended)
- **CUDA**: 12.9+ (included with driver)
- **OptiX**: 9.0+ (included with driver)
- **CMake**: 3.20+
- **Ruby**: 3.4+ (for SketchUp plugin development)

## 🚀 **Quick Start**

### **1. Clone Repository**
```bash
git clone https://github.com/photonrender/photonrender.git
cd photonrender
```

### **2. Configure Build**
```bash
mkdir build && cd build
cmake .. -DUSE_CUDA=ON -DUSE_OPTIX=ON -DBUILD_RUBY_BINDINGS=ON
```

### **3. Build Project**
```bash
# Build with Visual Studio
cmake --build . --config Release

# Or open PhotonRender.sln in Visual Studio 2022
```

### **4. Run Tests**
```bash
# Run test suite
ctest --output-on-failure

# Expected output: 5/5 tests passing
```

## 🔧 **Build Configuration**

### **CMake Options**
- `USE_CUDA=ON`: Enable CUDA acceleration
- `USE_OPTIX=ON`: Enable OptiX hardware ray tracing
- `BUILD_RUBY_BINDINGS=ON`: Build SketchUp plugin bindings
- `BUILD_TESTS=ON`: Build test suite (default)
- `BUILD_BENCHMARKS=OFF`: Build performance benchmarks

### **Dependencies**
- **Embree 4.3**: CPU ray tracing (Apache 2.0)
- **CUDA 12.9**: GPU computing platform
- **OptiX 9.0**: Hardware ray tracing
- **TBB**: Threading Building Blocks
- **stb_image**: Image I/O library
- **JSON**: Configuration and scene data

## 📁 **Project Structure**

```
photon-render/
├── 📁 src/                    # Source code
│   ├── 📁 core/               # C++ rendering engine
│   │   ├── 📁 math/           # Math library (Vec3, Matrix4)
│   │   ├── 📁 scene/          # Scene management
│   │   ├── 📁 materials/      # Material system
│   │   ├── 📁 lights/         # Lighting system
│   │   ├── 📁 integrator/     # Rendering algorithms
│   │   └── 📄 renderer.cpp    # Main renderer
│   ├── 📁 gpu/                # CUDA/OptiX kernels
│   ├── 📁 bindings/           # Ruby-C++ bridge
│   └── 📁 ruby/               # SketchUp plugin
├── 📁 include/photon/         # Public headers
├── 📁 tests/                  # Test suite
└── 📁 build/                  # Build output
```

## 🔌 **SketchUp Integration**

### **Ruby Plugin Architecture**
```ruby
# Main plugin file: photon_render.rb
module PhotonRender
  # Core modules
  require 'photon_render/menu'          # Menu system (20+ commands)
  require 'photon_render/toolbar'       # Toolbar (8 buttons)
  require 'photon_render/dialog'        # HTML dialogs
  require 'photon_render/geometry_export' # Geometry conversion
end
```

### **Geometry Export System**
- **Face-to-Triangle**: Automatic triangulation of SketchUp faces
- **Material Mapping**: SketchUp materials → PBR conversion
- **Transform Handling**: Groups and components support
- **Camera Export**: Automatic camera parameter extraction

### **UI Components**
- **Menu System**: 20+ organized commands in Plugins menu
- **Toolbar**: 8 buttons for quick access (render, settings, etc.)
- **Settings Dialog**: HTML-based configuration interface
- **Progress Dialog**: Real-time render feedback with statistics

## 🎨 **Rendering Features**

### **Current Implementation (Phase 3.1 Complete)**
- **Path Tracing**: Monte Carlo global illumination
- **Multiple Integrators**: Path tracing, direct lighting, AO
- **Material System**: 4 material types (Diffuse, Mirror, Emissive, Plastic)
- **Lighting**: 5 light types (Point, Directional, Area, Environment, Spot)
- **Sampling**: Random, Stratified, Halton sequences

### **Next Phase Features (Phase 3.2)**
- **Disney PBR Materials**: Metallic-roughness workflow
- **HDRI Environment**: High dynamic range lighting
- **Advanced Textures**: UV mapping, procedural textures
- **Real-time Material Editor**: Interactive preview interface

## 🧪 **Testing**

### **Test Suite**
```bash
# Run all tests
ctest --output-on-failure

# Run specific test categories
ctest -R "math"        # Math library tests
ctest -R "scene"       # Scene management tests
ctest -R "render"      # Rendering tests
```

### **Performance Benchmarks**
```bash
# Build benchmarks
cmake .. -DBUILD_BENCHMARKS=ON
cmake --build . --config Release

# Run benchmarks
./bin/photon_benchmark
```

## 🐛 **Troubleshooting**

### **Common Build Issues**

#### CUDA Not Found
```bash
# Ensure CUDA is in PATH
set PATH=%PATH%;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin
```

#### OptiX Headers Missing
```bash
# OptiX headers are included with NVIDIA drivers 545.84+
# Update to latest driver if missing
```

#### Ruby Bindings Compilation
```bash
# Ensure Ruby development files are installed
ruby -e "puts RbConfig::CONFIG['rubyhdrdir']"
```

### **Performance Issues**

#### Low GPU Utilization
- Check GPU memory usage (should be <80% of VRAM)
- Verify CUDA/OptiX drivers are up to date
- Monitor thermal throttling

#### Slow Rendering
- Reduce samples per pixel for preview
- Use smaller tile sizes for better GPU utilization
- Enable GPU acceleration in settings

## 📚 **API Reference**

### **Core Renderer API**
```cpp
#include "photon/photon.hpp"

// Initialize PhotonRender
photon::initialize();

// Create renderer
auto renderer = std::make_shared<photon::Renderer>();

// Configure settings
photon::RenderSettings settings;
settings.width = 1920;
settings.height = 1080;
settings.samplesPerPixel = 100;
settings.useGPU = true;

renderer->setSettings(settings);

// Render scene
renderer->render();
```

### **Ruby Plugin API**
```ruby
# SketchUp plugin integration
require 'photon_render'

# Start render with settings
settings = {
  width: 1920,
  height: 1080,
  samples_per_pixel: 100,
  use_gpu: true
}

PhotonRender.render_manager.start_render(settings)
```

## 🔗 **Resources**

### **Documentation**
- [Project Overview](app_map.md) - Complete project structure
- [Phase 3.1 Report](phase3-1-completion-report.md) - Latest achievements
- [Next Session Guide](session-handover-phase3-2.md) - Phase 3.2 preparation

### **External Resources**
- [NVIDIA OptiX Documentation](https://developer.nvidia.com/optix)
- [Intel Embree Documentation](https://embree.github.io/)
- [SketchUp Ruby API](https://ruby.sketchup.com/)
- [CUDA Programming Guide](https://docs.nvidia.com/cuda/)

### **Community**
- [GitHub Repository](https://github.com/photonrender/photonrender)
- [Issues & Bug Reports](https://github.com/photonrender/photonrender/issues)
- [Discussions](https://github.com/photonrender/photonrender/discussions)

---

**Technical Guide** | **Version**: 3.0.0-alpha | **Updated**: 2025-06-20
**Status**: Phase 3.1 Complete - Ready for Phase 3.2 Advanced Rendering