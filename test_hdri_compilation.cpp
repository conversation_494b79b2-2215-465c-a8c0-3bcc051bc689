// test_hdri_compilation.cpp
// Simple compilation test for HDRI Environment Lighting

#include "src/core/texture/hdr_texture.hpp"
#include "src/core/light/hdri_environment_light.hpp"
#include "src/core/math/vec3.hpp"
#include "src/core/math/color3.hpp"

#include <iostream>

using namespace photon;

int main() {
    std::cout << "🌟 HDRI Environment Lighting Compilation Test" << std::endl;
    std::cout << "Phase 3.2.2 Task 1 - Basic Functionality" << std::endl;
    std::cout << "==========================================" << std::endl;
    
    bool allPassed = true;
    
    try {
        // Test HDR Texture creation
        std::cout << "Testing HDR Texture creation... ";
        auto hdrTexture = HDRTextureFactory::createSky(
            Color3(0.5f, 0.7f, 1.0f), Color3(0.8f, 0.9f, 1.0f), 10.0f);
        
        if (hdrTexture && hdrTexture->isLoaded()) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test HDR Texture sampling
        std::cout << "Testing HDR Texture sampling... ";
        Vec2 uv(0.5f, 0.5f);
        Color3 sample = hdrTexture->sample(uv);
        
        if (sample.isValid() && sample.maxComponent() > 0.0f) {
            std::cout << "✅ OK (sample: " << sample.r << "," << sample.g << "," << sample.b << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test direction conversion
        std::cout << "Testing direction conversion... ";
        Vec3 direction(0, 1, 0);
        Vec2 convertedUV = hdrTexture->directionToUV(direction);
        Vec3 backDirection = hdrTexture->uvToDirection(convertedUV);
        
        float error = (direction - backDirection).length();
        if (error < 0.01f) {
            std::cout << "✅ OK (error: " << error << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED (error: " << error << ")" << std::endl;
            allPassed = false;
        }
        
        // Test HDRI Environment Light creation
        std::cout << "Testing HDRI Environment Light creation... ";
        auto envLight = HDRIEnvironmentLightFactory::createSky();
        
        if (envLight && envLight->getName() == "HDRIEnvironment") {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test environment evaluation
        std::cout << "Testing environment evaluation... ";
        Vec3 testDir(0, 1, 0);
        Color3 radiance = envLight->evaluate(testDir);
        
        if (radiance.isValid() && radiance.maxComponent() > 0.0f) {
            std::cout << "✅ OK (radiance: " << radiance.r << "," << radiance.g << "," << radiance.b << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test power calculation
        std::cout << "Testing power calculation... ";
        Color3 power = envLight->power();
        
        if (power.isValid() && power.maxComponent() > 0.0f) {
            std::cout << "✅ OK (power: " << power.maxComponent() << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test importance sampling setup
        std::cout << "Testing importance sampling setup... ";
        envLight->setImportanceSampling(true);
        bool importanceEnabled = envLight->isImportanceSamplingEnabled();
        
        if (importanceEnabled) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test rotation
        std::cout << "Testing environment rotation... ";
        float originalRotation = envLight->getRotation();
        envLight->setRotation(M_PI / 4.0f);
        float newRotation = envLight->getRotation();
        
        if (std::abs(newRotation - M_PI / 4.0f) < 0.01f) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test intensity scaling
        std::cout << "Testing intensity scaling... ";
        envLight->setIntensity(2.0f);
        float intensity = envLight->getIntensity();
        
        if (std::abs(intensity - 2.0f) < 0.01f) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cout << "❌ Unknown exception" << std::endl;
        allPassed = false;
    }
    
    std::cout << std::endl;
    if (allPassed) {
        std::cout << "🎉 ALL COMPILATION TESTS PASSED!" << std::endl;
        std::cout << "✅ HDRI Environment Lighting system working correctly" << std::endl;
        std::cout << "🚀 Task 1 Phase 3.2.2 implementation successful!" << std::endl;
        std::cout << "🌟 Ready for Task 2: Area Lights Implementation" << std::endl;
    } else {
        std::cout << "❌ Some compilation tests failed" << std::endl;
        std::cout << "🔧 Additional fixes may be needed" << std::endl;
    }
    
    return allPassed ? 0 : 1;
}
