// test_area_lights_compilation.cpp
// Simple compilation test for Area Lights

#include "src/core/light/area_light_base.hpp"
#include "src/core/light/rectangle_light.hpp"
#include "src/core/light/disk_light.hpp"
#include "src/core/light/sphere_light.hpp"
#include "src/core/math/vec3.hpp"
#include "src/core/math/color3.hpp"

#include <iostream>

using namespace photon;

int main() {
    std::cout << "💡 Area Lights Compilation Test" << std::endl;
    std::cout << "Phase 3.2.2 Task 2 - Area Lights Implementation" << std::endl;
    std::cout << "===============================================" << std::endl;
    
    bool allPassed = true;
    
    try {
        // Test Rectangle Light creation
        std::cout << "Testing Rectangle Light creation... ";
        auto rectLight = RectangleLightFactory::fromCenterAndSize(
            Vec3(0, 5, 0), Vec3(0, -1, 0), 2.0f, 1.0f, Color3(1.0f, 0.8f, 0.6f));
        
        if (rectLight && rectLight->getName() == "Rectangle") {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Rectangle Light properties
        std::cout << "Testing Rectangle Light properties... ";
        float area = rectLight->getArea();
        AreaLightShape shape = rectLight->getShape();
        
        if (area > 0.0f && shape == AreaLightShape::RECTANGLE) {
            std::cout << "✅ OK (area: " << area << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Disk Light creation
        std::cout << "Testing Disk Light creation... ";
        auto diskLight = DiskLightFactory::create(
            Vec3(0, 3, 0), Vec3(0, -1, 0), 1.5f, Color3(0.8f, 0.9f, 1.0f));
        
        if (diskLight && diskLight->getName() == "Disk") {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Disk Light properties
        std::cout << "Testing Disk Light properties... ";
        float diskArea = diskLight->getArea();
        float expectedDiskArea = M_PI * 1.5f * 1.5f;
        
        if (std::abs(diskArea - expectedDiskArea) < 0.01f) {
            std::cout << "✅ OK (area: " << diskArea << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED (expected: " << expectedDiskArea << ", got: " << diskArea << ")" << std::endl;
            allPassed = false;
        }
        
        // Test Sphere Light creation
        std::cout << "Testing Sphere Light creation... ";
        auto sphereLight = SphereLightFactory::create(
            Vec3(0, 4, 0), 0.8f, Color3(1.0f, 1.0f, 0.8f));
        
        if (sphereLight && sphereLight->getName() == "Sphere") {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Sphere Light properties
        std::cout << "Testing Sphere Light properties... ";
        float sphereArea = sphereLight->getArea();
        float expectedSphereArea = 4.0f * M_PI * 0.8f * 0.8f;
        
        if (std::abs(sphereArea - expectedSphereArea) < 0.01f) {
            std::cout << "✅ OK (area: " << sphereArea << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED (expected: " << expectedSphereArea << ", got: " << sphereArea << ")" << std::endl;
            allPassed = false;
        }
        
        // Test Factory Functions
        std::cout << "Testing Factory Functions... ";
        auto ceilingLight = RectangleLightFactory::createCeiling(Vec3(0, 6, 0), 3.0f, 2.0f, Color3(1.0f));
        auto bulbLight = DiskLightFactory::createBulb(Vec3(0, 2, 0), 0.3f, Color3(1.0f, 0.9f, 0.7f));
        auto sunLight = SphereLightFactory::createSun(Vec3(100, 100, 0), 10.0f, Color3(1.0f, 0.95f, 0.8f));
        
        if (ceilingLight && bulbLight && sunLight) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Area Light Utilities
        std::cout << "Testing Area Light Utilities... ";
        Vec2 diskSample = AreaLightUtils::sampleUniformDisk(Vec2(0.5f, 0.7f));
        Vec3 sphereSample = AreaLightUtils::sampleUniformSphere(Vec2(0.3f, 0.8f));
        
        if (diskSample.length() <= 1.0f && sphereSample.length() <= 1.01f) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Two-sided emission
        std::cout << "Testing Two-sided emission... ";
        auto twoSidedLight = RectangleLightFactory::fromCenterAndSize(
            Vec3(0, 0, 0), Vec3(1, 0, 0), 1.0f, 1.0f, Color3(1.0f), 1.0f, true);
        
        if (twoSidedLight && twoSidedLight->isTwoSided()) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Intensity scaling
        std::cout << "Testing Intensity scaling... ";
        rectLight->setIntensity(2.5f);
        float intensity = rectLight->getIntensity();
        
        if (std::abs(intensity - 2.5f) < 0.01f) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Emission color
        std::cout << "Testing Emission color... ";
        Color3 emission = rectLight->getEmission();
        Color3 effectiveEmission = rectLight->getEffectiveEmission();
        
        if (emission.isValid() && effectiveEmission.isValid()) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
        // Test Power calculation
        std::cout << "Testing Power calculation... ";
        Color3 power = rectLight->power();
        
        if (power.isValid() && power.maxComponent() > 0.0f) {
            std::cout << "✅ OK (power: " << power.maxComponent() << ")" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cout << "❌ Unknown exception" << std::endl;
        allPassed = false;
    }
    
    std::cout << std::endl;
    if (allPassed) {
        std::cout << "🎉 ALL COMPILATION TESTS PASSED!" << std::endl;
        std::cout << "✅ Area Lights system working correctly" << std::endl;
        std::cout << "💡 Rectangle, Disk, and Sphere lights implemented" << std::endl;
        std::cout << "🌫️ Soft lighting and area sampling ready" << std::endl;
        std::cout << "🚀 Task 2 Phase 3.2.2 implementation successful!" << std::endl;
        std::cout << "🎯 Ready for Task 3: Multiple Importance Sampling" << std::endl;
    } else {
        std::cout << "❌ Some compilation tests failed" << std::endl;
        std::cout << "🔧 Additional fixes may be needed" << std::endl;
    }
    
    return allPassed ? 0 : 1;
}
