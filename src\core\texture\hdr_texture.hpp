// src/core/texture/hdr_texture.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// HDR texture loading and sampling for environment lighting

#pragma once

#include "../math/vec2.hpp"
#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include <string>
#include <vector>
#include <memory>

namespace photon {

/**
 * @brief HDR texture formats supported
 */
enum class HDRFormat {
    HDR,        // Radiance HDR (.hdr)
    EXR,        // OpenEXR (.exr)
    RGBE,       // RGBE format
    AUTO        // Auto-detect from extension
};

/**
 * @brief HDR texture class for environment lighting
 */
class HDRTexture {
public:
    /**
     * @brief Constructor
     */
    HDRTexture();
    
    /**
     * @brief Destructor
     */
    ~HDRTexture();
    
    /**
     * @brief Load HDR texture from file
     * @param filename Path to HDR file
     * @param format HDR format (AUTO for auto-detection)
     * @return True if loaded successfully
     */
    bool load(const std::string& filename, HDRFormat format = HDRFormat::AUTO);
    
    /**
     * @brief Create HDR texture from data
     * @param data RGB float data
     * @param width Texture width
     * @param height Texture height
     * @return True if created successfully
     */
    bool create(const std::vector<float>& data, int width, int height);
    
    /**
     * @brief Sample texture at UV coordinates
     * @param uv UV coordinates [0,1]
     * @return Sampled color
     */
    Color3 sample(const Vec2& uv) const;
    
    /**
     * @brief Sample texture with filtering
     * @param uv UV coordinates [0,1]
     * @param filter Filtering mode
     * @return Sampled color
     */
    Color3 sampleFiltered(const Vec2& uv, bool bilinear = true) const;
    
    /**
     * @brief Convert direction to UV coordinates (equirectangular)
     * @param direction World direction
     * @return UV coordinates
     */
    Vec2 directionToUV(const Vec3& direction) const;
    
    /**
     * @brief Convert UV coordinates to direction (equirectangular)
     * @param uv UV coordinates
     * @return World direction
     */
    Vec3 uvToDirection(const Vec2& uv) const;
    
    /**
     * @brief Sample texture by direction
     * @param direction World direction
     * @return Sampled color
     */
    Color3 sampleDirection(const Vec3& direction) const;
    
    /**
     * @brief Get texture dimensions
     */
    int getWidth() const { return m_width; }
    int getHeight() const { return m_height; }
    
    /**
     * @brief Check if texture is loaded
     */
    bool isLoaded() const { return !m_data.empty(); }
    
    /**
     * @brief Get average luminance
     */
    float getAverageLuminance() const { return m_averageLuminance; }
    
    /**
     * @brief Get maximum luminance
     */
    float getMaxLuminance() const { return m_maxLuminance; }
    
    /**
     * @brief Build importance sampling data
     */
    void buildImportanceSampling();
    
    /**
     * @brief Sample direction using importance sampling
     * @param u Random sample [0,1]^2
     * @param pdf Output PDF
     * @return Sampled direction
     */
    Vec3 sampleImportance(const Vec2& u, float& pdf) const;
    
    /**
     * @brief Get PDF for importance sampling
     * @param direction World direction
     * @return PDF value
     */
    float getImportancePDF(const Vec3& direction) const;
    
    /**
     * @brief Set rotation (in radians)
     */
    void setRotation(float rotation) { m_rotation = rotation; }
    
    /**
     * @brief Get rotation
     */
    float getRotation() const { return m_rotation; }
    
    /**
     * @brief Set intensity multiplier
     */
    void setIntensity(float intensity) { m_intensity = intensity; }
    
    /**
     * @brief Get intensity multiplier
     */
    float getIntensity() const { return m_intensity; }

private:
    int m_width = 0;
    int m_height = 0;
    std::vector<float> m_data;  // RGB float data
    
    // Environment parameters
    float m_rotation = 0.0f;    // Environment rotation
    float m_intensity = 1.0f;   // Intensity multiplier
    
    // Precomputed statistics
    float m_averageLuminance = 0.0f;
    float m_maxLuminance = 0.0f;
    
    // Importance sampling data
    std::vector<float> m_luminanceMap;      // Per-pixel luminance
    std::vector<float> m_rowCDF;            // Row cumulative distribution
    std::vector<std::vector<float>> m_colCDF; // Column CDFs per row
    bool m_importanceSamplingBuilt = false;
    
    /**
     * @brief Load HDR format
     */
    bool loadHDR(const std::string& filename);
    
    /**
     * @brief Load EXR format
     */
    bool loadEXR(const std::string& filename);
    
    /**
     * @brief Load RGBE format
     */
    bool loadRGBE(const std::string& filename);
    
    /**
     * @brief Detect format from filename
     */
    HDRFormat detectFormat(const std::string& filename) const;
    
    /**
     * @brief Compute texture statistics
     */
    void computeStatistics();
    
    /**
     * @brief Apply rotation to UV coordinates
     */
    Vec2 applyRotation(const Vec2& uv) const;
    
    /**
     * @brief Bilinear interpolation
     */
    Color3 bilinearSample(float u, float v) const;
    
    /**
     * @brief Get pixel at coordinates (with wrapping)
     */
    Color3 getPixel(int x, int y) const;
    
    /**
     * @brief Sample from CDF using binary search
     */
    int sampleCDF(const std::vector<float>& cdf, float u) const;
};

/**
 * @brief HDR texture factory functions
 */
namespace HDRTextureFactory {
    /**
     * @brief Create solid color HDR texture
     */
    std::shared_ptr<HDRTexture> createSolid(const Color3& color, int width = 64, int height = 32);
    
    /**
     * @brief Create gradient HDR texture
     */
    std::shared_ptr<HDRTexture> createGradient(const Color3& top, const Color3& bottom, 
                                              int width = 256, int height = 128);
    
    /**
     * @brief Create sky HDR texture
     */
    std::shared_ptr<HDRTexture> createSky(const Color3& zenith, const Color3& horizon, 
                                         float sunIntensity = 10.0f, int width = 512, int height = 256);
}

} // namespace photon
