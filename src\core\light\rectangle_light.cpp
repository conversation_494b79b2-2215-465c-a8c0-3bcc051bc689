// src/core/light/rectangle_light.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Rectangle Area Light implementation

#include "rectangle_light.hpp"
#include "../sampler/sampler.hpp"
#include "../math/ray.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

namespace photon {

RectangleLight::RectangleLight(const Vec3& corner, const Vec3& edge1, const Vec3& edge2,
                               const Color3& emission, float intensity, bool twoSided)
    : AreaLightBase(emission, intensity, twoSided), m_corner(corner), m_edge1(edge1), m_edge2(edge2) {
    updateCachedValues();
}

RectangleLight::RectangleLight(const Vec3& center, const Vec3& normal, float width, float height,
                               const Color3& emission, float intensity, bool twoSided)
    : AreaLightBase(emission, intensity, twoSided) {
    setGeometry(center, normal, width, height);
}

void RectangleLight::sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const {
    Vec2 u = sampler.next2D();
    
    if (m_samplingMode == AreaLightSampling::UNIFORM) {
        // Uniform sampling
        point = sampleUniform(u);
        pdf = 1.0f / m_area;
    } else {
        // Cosine-weighted sampling (not implemented for area lights typically)
        point = sampleUniform(u);
        pdf = 1.0f / m_area;
    }
    
    normal = m_normal;
}

float RectangleLight::getArea() const {
    return m_area;
}

bool RectangleLight::intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const {
    // Intersect ray with rectangle plane
    float denom = ray.direction.dot(m_normal);
    if (std::abs(denom) < 1e-6f) {
        return false; // Ray is parallel to plane
    }
    
    float t_plane = (m_corner - ray.origin).dot(m_normal) / denom;
    if (t_plane < 0.0f) {
        return false; // Intersection behind ray origin
    }
    
    // Compute intersection point
    Vec3 hitPoint = ray.origin + t_plane * ray.direction;
    
    // Check if point is inside rectangle
    Vec2 localPoint = worldToLocal(hitPoint);
    if (!isInsideRectangle(localPoint)) {
        return false; // Outside rectangle bounds
    }
    
    t = t_plane;
    point = hitPoint;
    normal = m_normal;
    
    return true;
}

void RectangleLight::getCorners(Vec3 corners[4]) const {
    corners[0] = m_corner;
    corners[1] = m_corner + m_edge1;
    corners[2] = m_corner + m_edge1 + m_edge2;
    corners[3] = m_corner + m_edge2;
}

void RectangleLight::setGeometry(const Vec3& corner, const Vec3& edge1, const Vec3& edge2) {
    m_corner = corner;
    m_edge1 = edge1;
    m_edge2 = edge2;
    updateCachedValues();
}

void RectangleLight::setGeometry(const Vec3& center, const Vec3& normal, float width, float height) {
    // Create orthonormal basis
    Vec3 n = normal.normalized();
    Vec3 u, v;
    
    if (std::abs(n.x) > 0.1f) {
        u = Vec3(0, 1, 0).cross(n).normalized();
    } else {
        u = Vec3(1, 0, 0).cross(n).normalized();
    }
    v = n.cross(u);
    
    // Set rectangle geometry
    Vec3 halfWidth = u * (width * 0.5f);
    Vec3 halfHeight = v * (height * 0.5f);
    
    m_corner = center - halfWidth - halfHeight;
    m_edge1 = u * width;
    m_edge2 = v * height;
    
    updateCachedValues();
}

Vec3 RectangleLight::sampleUniform(const Vec2& u) const {
    return m_corner + u.x * m_edge1 + u.y * m_edge2;
}

std::pair<Vec3, float> RectangleLight::sampleCosineWeighted(const Vec2& u, const Vec3& fromPoint) const {
    // For area lights, cosine-weighted sampling is typically done from the surface
    // This is a simplified implementation
    Vec3 point = sampleUniform(u);
    float pdf = 1.0f / m_area;
    
    return std::make_pair(point, pdf);
}

float RectangleLight::getSolidAngle(const Vec3& point) const {
    Vec3 corners[4];
    getCorners(corners);
    return AreaLightUtils::rectangleSolidAngle(point, corners);
}

void RectangleLight::updateCachedValues() {
    m_normal = m_edge1.cross(m_edge2).normalized();
    m_area = m_edge1.cross(m_edge2).length();
}

bool RectangleLight::isInsideRectangle(const Vec2& localPoint) const {
    return (localPoint.x >= 0.0f && localPoint.x <= 1.0f &&
            localPoint.y >= 0.0f && localPoint.y <= 1.0f);
}

Vec2 RectangleLight::worldToLocal(const Vec3& worldPoint) const {
    Vec3 relative = worldPoint - m_corner;
    
    // Project onto edge vectors
    float u = relative.dot(m_edge1) / m_edge1.dot(m_edge1);
    float v = relative.dot(m_edge2) / m_edge2.dot(m_edge2);
    
    return Vec2(u, v);
}

Vec3 RectangleLight::localToWorld(const Vec2& localPoint) const {
    return m_corner + localPoint.x * m_edge1 + localPoint.y * m_edge2;
}

// Factory functions
namespace RectangleLightFactory {

std::shared_ptr<RectangleLight> fromCornerAndEdges(
    const Vec3& corner, const Vec3& edge1, const Vec3& edge2,
    const Color3& emission, float intensity, bool twoSided) {
    return std::make_shared<RectangleLight>(corner, edge1, edge2, emission, intensity, twoSided);
}

std::shared_ptr<RectangleLight> fromCenterAndSize(
    const Vec3& center, const Vec3& normal, float width, float height,
    const Color3& emission, float intensity, bool twoSided) {
    return std::make_shared<RectangleLight>(center, normal, width, height, emission, intensity, twoSided);
}

std::shared_ptr<RectangleLight> createSquare(
    const Vec3& center, const Vec3& normal, float size,
    const Color3& emission, float intensity, bool twoSided) {
    return fromCenterAndSize(center, normal, size, size, emission, intensity, twoSided);
}

std::shared_ptr<RectangleLight> createCeiling(
    const Vec3& center, float width, float height,
    const Color3& emission, float intensity) {
    Vec3 normal(0, -1, 0); // Facing down
    return fromCenterAndSize(center, normal, width, height, emission, intensity, false);
}

std::shared_ptr<RectangleLight> createWall(
    const Vec3& center, const Vec3& wallNormal, float width, float height,
    const Color3& emission, float intensity) {
    return fromCenterAndSize(center, wallNormal, width, height, emission, intensity, false);
}

} // namespace RectangleLightFactory

} // namespace photon
