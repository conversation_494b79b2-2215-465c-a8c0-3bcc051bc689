// src/test_disney_simple.cpp
// PhotonRender - Simple Disney BRDF Test (Standalone)

#include <iostream>
#include <vector>
#include <memory>
#include <cmath>
#include <algorithm>

// Minimal dependencies - only what we need for Disney BRDF
namespace photon {

// Basic Vec3 class
struct Vec3 {
    float x, y, z;
    
    Vec3() : x(0), y(0), z(0) {}
    Vec3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    Vec3 operator+(const Vec3& v) const { return Vec3(x + v.x, y + v.y, z + v.z); }
    Vec3 operator-(const Vec3& v) const { return Vec3(x - v.x, y - v.y, z - v.z); }
    Vec3 operator*(float s) const { return Vec3(x * s, y * s, z * s); }
    Vec3 operator/(float s) const { return Vec3(x / s, y / s, z / s); }
    
    float dot(const Vec3& v) const { return x * v.x + y * v.y + z * v.z; }
    Vec3 cross(const Vec3& v) const { return Vec3(y * v.z - z * v.y, z * v.x - x * v.z, x * v.y - y * v.x); }
    float length() const { return std::sqrt(x * x + y * y + z * z); }
    Vec3 normalized() const { float l = length(); return l > 0 ? *this / l : Vec3(); }
    
    static Vec3 reflect(const Vec3& v, const Vec3& n) { return v - n * (2.0f * v.dot(n)); }
};

// Basic Color3 class
struct Color3 {
    float r, g, b;
    
    Color3() : r(0), g(0), b(0) {}
    Color3(float r, float g, float b) : r(r), g(g), b(b) {}
    Color3(float gray) : r(gray), g(gray), b(gray) {}
    
    Color3 operator+(const Color3& c) const { return Color3(r + c.r, g + c.g, b + c.b); }
    Color3 operator-(const Color3& c) const { return Color3(r - c.r, g - c.g, b - c.b); }
    Color3 operator*(float s) const { return Color3(r * s, g * s, b * s); }
    Color3 operator*(const Color3& c) const { return Color3(r * c.r, g * c.g, b * c.b); }
    Color3 operator/(float s) const { return Color3(r / s, g / s, b / s); }
    
    float luminance() const { return 0.299f * r + 0.587f * g + 0.114f * b; }
    float length() const { return std::sqrt(r * r + g * g + b * b); }
    bool isZero() const { return r == 0 && g == 0 && b == 0; }
    Color3 normalized() const { float l = luminance(); return l > 0 ? *this / l : Color3(); }
};

// Simple random sampler
class SimpleSampler {
private:
    mutable unsigned int seed;
    
public:
    SimpleSampler() : seed(12345) {}
    
    float get1D() const {
        seed = seed * 1103515245 + 12345;
        return (seed / 65536) % 32768 / 32768.0f;
    }
    
    Vec3 get2D() const {
        return Vec3(get1D(), get1D(), 0);
    }
};

} // namespace photon

// Include our Disney BRDF implementation
#include "core/material/disney_brdf.hpp"

using namespace photon;

/**
 * @brief Test Disney BRDF basic functionality
 */
bool testDisneyBRDFBasic() {
    std::cout << "=== Testing Disney BRDF Basic Functionality ===" << std::endl;
    
    try {
        // Create Disney BRDF
        DisneyBRDF brdf;
        std::cout << "✅ Disney BRDF created successfully" << std::endl;
        
        // Test parameter setting
        DisneyBRDFParams params;
        params.baseColor = Color3(0.8f, 0.6f, 0.4f);
        params.metallic = 0.0f;
        params.roughness = 0.3f;
        params.specular = 0.5f;
        params.validate();
        
        brdf.setParameters(params);
        std::cout << "✅ Parameters set: baseColor=(" << params.baseColor.r << "," 
                  << params.baseColor.g << "," << params.baseColor.b << "), metallic=" 
                  << params.metallic << ", roughness=" << params.roughness << std::endl;
        
        // Test BRDF evaluation
        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);
        Vec3 wi(0.5f, 0.5f, 0.707f);
        wi = wi.normalized();
        
        Color3 f = brdf.eval(wo, wi, n);
        std::cout << "✅ BRDF evaluation: f = (" << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
        
        // Test BRDF sampling
        SimpleSampler sampler;
        Vec3 sampledWi;
        float pdf;
        Color3 sampledF = brdf.sample(wo, n, sampler, sampledWi, pdf);
        std::cout << "✅ BRDF sampling: f = (" << sampledF.r << ", " << sampledF.g << ", " 
                  << sampledF.b << "), pdf = " << pdf << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Error: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test material presets
 */
bool testMaterialPresets() {
    std::cout << "\n=== Testing Material Presets ===" << std::endl;
    
    std::vector<std::string> presetNames = {"plastic", "metal", "glass", "wood", "fabric"};
    
    for (const auto& name : presetNames) {
        try {
            DisneyBRDFParams params;
            
            if (name == "plastic") {
                params = DisneyMaterialPresets::createPlastic(Color3(0.8f, 0.2f, 0.2f));
            } else if (name == "metal") {
                params = DisneyMaterialPresets::createMetal(Color3(0.8f, 0.6f, 0.4f));
            } else if (name == "glass") {
                params = DisneyMaterialPresets::createGlass(Color3(0.9f, 0.9f, 0.9f));
            } else if (name == "wood") {
                params = DisneyMaterialPresets::createWood(Color3(0.6f, 0.4f, 0.2f));
            } else if (name == "fabric") {
                params = DisneyMaterialPresets::createFabric(Color3(0.2f, 0.4f, 0.8f));
            }
            
            // Test energy conservation
            bool energyOK = DisneyMaterialPresets::validateEnergyConservation(params);
            
            std::cout << "✅ " << name << " preset: metallic=" << params.metallic 
                      << ", roughness=" << params.roughness << ", energy=" 
                      << (energyOK ? "OK" : "FAIL") << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "❌ " << name << " preset failed: " << e.what() << std::endl;
            return false;
        }
    }
    
    return true;
}

/**
 * @brief Test energy conservation
 */
bool testEnergyConservation() {
    std::cout << "\n=== Testing Energy Conservation ===" << std::endl;
    
    DisneyBRDF brdf;
    SimpleSampler sampler;
    
    // Test plastic material
    DisneyBRDFParams params = DisneyMaterialPresets::createPlastic(Color3(0.8f, 0.6f, 0.4f));
    brdf.setParameters(params);
    
    Vec3 n(0, 0, 1);
    Vec3 wo(0, 0, 1);
    
    // Monte Carlo integration
    const int numSamples = 1000;
    Color3 totalReflectance(0.0f);
    int validSamples = 0;
    
    for (int i = 0; i < numSamples; ++i) {
        Vec3 wi;
        float pdf;
        Color3 f = brdf.sample(wo, n, sampler, wi, pdf);
        
        if (pdf > 0.0f && wi.dot(n) > 0.0f) {
            totalReflectance = totalReflectance + f * (wi.dot(n) / pdf);
            validSamples++;
        }
    }
    
    if (validSamples > 0) {
        totalReflectance = totalReflectance / float(validSamples);
        float maxReflectance = std::max({totalReflectance.r, totalReflectance.g, totalReflectance.b});
        
        bool energyOK = maxReflectance <= 1.1f; // Allow some Monte Carlo error
        std::cout << "✅ Energy conservation test: Max reflectance = " << maxReflectance 
                  << " (" << (energyOK ? "OK" : "FAIL") << "), Valid samples: " 
                  << validSamples << "/" << numSamples << std::endl;
        
        return energyOK;
    } else {
        std::cout << "❌ No valid samples generated" << std::endl;
        return false;
    }
}

/**
 * @brief Main test function
 */
int main() {
    std::cout << "PhotonRender Disney BRDF Standalone Test" << std::endl;
    std::cout << "========================================" << std::endl;
    
    bool allPassed = true;
    
    // Run tests
    allPassed &= testDisneyBRDFBasic();
    allPassed &= testMaterialPresets();
    allPassed &= testEnergyConservation();
    
    std::cout << "\n=== Test Results ===" << std::endl;
    if (allPassed) {
        std::cout << "🎉 All Disney BRDF tests PASSED!" << std::endl;
        std::cout << "✅ Disney BRDF implementation is working correctly!" << std::endl;
    } else {
        std::cout << "❌ Some Disney BRDF tests FAILED!" << std::endl;
    }
    
    return allPassed ? 0 : 1;
}
