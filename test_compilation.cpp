// test_compilation.cpp
// Simple compilation test for PhotonRender Phase 3.2.1 critical fixes

#include "src/core/math/color3.hpp"
#include "src/core/math/vec2.hpp"
#include "src/core/math/vec3.hpp"
#include "src/core/sampler/random_sampler.hpp"
#include "src/core/scene/intersection.hpp"

#include <iostream>

using namespace photon;

int main() {
    std::cout << "🔧 PhotonRender Compilation Test - Critical Fixes" << std::endl;
    std::cout << "=================================================" << std::endl;
    
    bool allPassed = true;
    
    try {
        // Test Color3
        std::cout << "Testing Color3... ";
        Color3 color(0.8f, 0.6f, 0.4f);
        Color3 result = color * 2.0f;
        float luminance = color.luminance();
        std::cout << "✅ OK (luminance: " << luminance << ")" << std::endl;
        
        // Test Vec2
        std::cout << "Testing Vec2... ";
        Vec2 uv(0.5f, 0.7f);
        Vec2 uvResult = uv + Vec2(0.1f, 0.2f);
        float length = uv.length();
        std::cout << "✅ OK (length: " << length << ")" << std::endl;
        
        // Test ExtendedRandomSampler
        std::cout << "Testing ExtendedRandomSampler... ";
        ExtendedRandomSampler sampler(12345);
        float sample1D = sampler.next1D();
        Vec2 sample2D = sampler.next2D();
        std::cout << "✅ OK (1D: " << sample1D << ", 2D: " << sample2D.x << "," << sample2D.y << ")" << std::endl;
        
        // Test Intersection
        std::cout << "Testing Intersection... ";
        Intersection isect;
        isect.p = Vec3(1.0f, 2.0f, 3.0f);
        isect.n = Vec3(0.0f, 0.0f, 1.0f);
        isect.t = 5.0f;
        isect.setUV(0.3f, 0.7f);
        Vec2 retrievedUV = isect.getUV();
        std::cout << "✅ OK (UV: " << retrievedUV.x << "," << retrievedUV.y << ")" << std::endl;
        
        // Test integration
        std::cout << "Testing integration... ";
        Color3 baseColor = Color3::white();
        Vec2 texCoords = Vec2(0.5f, 0.5f);
        ExtendedRandomSampler testSampler;
        Vec2 randomSample = testSampler.next2D();
        
        // Simple integration test
        bool integrationOK = (baseColor.isValid() && 
                             texCoords.length() > 0.0f && 
                             randomSample.x >= 0.0f && randomSample.x < 1.0f);
        
        if (integrationOK) {
            std::cout << "✅ OK" << std::endl;
        } else {
            std::cout << "❌ FAILED" << std::endl;
            allPassed = false;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception: " << e.what() << std::endl;
        allPassed = false;
    } catch (...) {
        std::cout << "❌ Unknown exception" << std::endl;
        allPassed = false;
    }
    
    std::cout << std::endl;
    if (allPassed) {
        std::cout << "🎉 ALL COMPILATION TESTS PASSED!" << std::endl;
        std::cout << "✅ Critical files are working correctly" << std::endl;
        std::cout << "🚀 Ready for Phase 3.2.1 completion validation" << std::endl;
    } else {
        std::cout << "❌ Some compilation tests failed" << std::endl;
        std::cout << "🔧 Additional fixes may be needed" << std::endl;
    }
    
    return allPassed ? 0 : 1;
}
