// src/core/math/vec3.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// 3D Vector mathematics implementation

#pragma once

#include <cmath>
#include <iostream>
#include <algorithm>

namespace photon {

/**
 * @brief 2D Vector class for texture coordinates and 2D operations
 */
class Vec2 {
public:
    float x, y;

    // Constructors
    Vec2() : x(0), y(0) {}
    Vec2(float v) : x(v), y(v) {}
    Vec2(float x, float y) : x(x), y(y) {}

    // Copy constructor and assignment
    Vec2(const Vec2& v) = default;
    Vec2& operator=(const Vec2& v) = default;

    // Array access
    float& operator[](int i) { return (&x)[i]; }
    const float& operator[](int i) const { return (&x)[i]; }

    // Arithmetic operators
    Vec2 operator+(const Vec2& v) const { return Vec2(x + v.x, y + v.y); }
    Vec2 operator-(const Vec2& v) const { return Vec2(x - v.x, y - v.y); }
    Vec2 operator*(float t) const { return Vec2(x * t, y * t); }
    Vec2 operator/(float t) const { return Vec2(x / t, y / t); }
    Vec2 operator-() const { return Vec2(-x, -y); }

    // Assignment operators
    Vec2& operator+=(const Vec2& v) { x += v.x; y += v.y; return *this; }
    Vec2& operator-=(const Vec2& v) { x -= v.x; y -= v.y; return *this; }
    Vec2& operator*=(float t) { x *= t; y *= t; return *this; }
    Vec2& operator/=(float t) { x /= t; y /= t; return *this; }

    // Comparison operators
    bool operator==(const Vec2& v) const { return x == v.x && y == v.y; }
    bool operator!=(const Vec2& v) const { return !(*this == v); }

    // Vector operations
    float length() const { return std::sqrt(x * x + y * y); }
    float lengthSquared() const { return x * x + y * y; }

    Vec2 normalized() const {
        float len = length();
        return len > 0 ? *this / len : Vec2(0);
    }

    void normalize() {
        float len = length();
        if (len > 0) *this /= len;
    }

    float dot(const Vec2& v) const { return x * v.x + y * v.y; }

    // Component-wise operations
    Vec2 abs() const { return Vec2(std::abs(x), std::abs(y)); }
    Vec2 min(const Vec2& v) const { return Vec2(std::min(x, v.x), std::min(y, v.y)); }
    Vec2 max(const Vec2& v) const { return Vec2(std::max(x, v.x), std::max(y, v.y)); }

    float minComponent() const { return std::min(x, y); }
    float maxComponent() const { return std::max(x, y); }

    // Utility functions
    bool isZero() const { return x == 0 && y == 0; }
    bool hasNaN() const { return std::isnan(x) || std::isnan(y); }
    bool hasInf() const { return std::isinf(x) || std::isinf(y); }

    // Static utility functions
    static Vec2 lerp(const Vec2& a, const Vec2& b, float t) {
        return a + (b - a) * t;
    }
};

// Non-member operators for Vec2
inline Vec2 operator*(float t, const Vec2& v) { return v * t; }

// Stream operators for Vec2
inline std::ostream& operator<<(std::ostream& os, const Vec2& v) {
    return os << "Vec2(" << v.x << ", " << v.y << ")";
}

/**
 * @brief 3D Vector class for mathematical operations
 *
 * Provides comprehensive 3D vector operations for rendering calculations.
 * Optimized for performance with SIMD-friendly layout.
 */
class Vec3 {
public:
    float x, y, z;
    
    // Constructors
    Vec3() : x(0), y(0), z(0) {}
    Vec3(float v) : x(v), y(v), z(v) {}
    Vec3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    // Copy constructor and assignment
    Vec3(const Vec3& v) = default;
    Vec3& operator=(const Vec3& v) = default;
    
    // Array access
    float& operator[](int i) { return (&x)[i]; }
    const float& operator[](int i) const { return (&x)[i]; }
    
    // Arithmetic operators
    Vec3 operator+(const Vec3& v) const { return Vec3(x + v.x, y + v.y, z + v.z); }
    Vec3 operator-(const Vec3& v) const { return Vec3(x - v.x, y - v.y, z - v.z); }
    Vec3 operator*(const Vec3& v) const { return Vec3(x * v.x, y * v.y, z * v.z); }
    Vec3 operator/(const Vec3& v) const { return Vec3(x / v.x, y / v.y, z / v.z); }
    
    Vec3 operator*(float t) const { return Vec3(x * t, y * t, z * t); }
    Vec3 operator/(float t) const { float inv = 1.0f / t; return Vec3(x * inv, y * inv, z * inv); }
    
    // Unary operators
    Vec3 operator-() const { return Vec3(-x, -y, -z); }
    
    // Compound assignment operators
    Vec3& operator+=(const Vec3& v) { x += v.x; y += v.y; z += v.z; return *this; }
    Vec3& operator-=(const Vec3& v) { x -= v.x; y -= v.y; z -= v.z; return *this; }
    Vec3& operator*=(const Vec3& v) { x *= v.x; y *= v.y; z *= v.z; return *this; }
    Vec3& operator/=(const Vec3& v) { x /= v.x; y /= v.y; z /= v.z; return *this; }
    
    Vec3& operator*=(float t) { x *= t; y *= t; z *= t; return *this; }
    Vec3& operator/=(float t) { float inv = 1.0f / t; x *= inv; y *= inv; z *= inv; return *this; }
    
    // Comparison operators
    bool operator==(const Vec3& v) const { return x == v.x && y == v.y && z == v.z; }
    bool operator!=(const Vec3& v) const { return !(*this == v); }
    
    // Vector operations
    float length() const { return std::sqrt(x * x + y * y + z * z); }
    float lengthSquared() const { return x * x + y * y + z * z; }
    
    Vec3 normalized() const { 
        float len = length(); 
        return len > 0 ? *this / len : Vec3(0); 
    }
    
    void normalize() { 
        float len = length(); 
        if (len > 0) *this /= len; 
    }
    
    float dot(const Vec3& v) const { return x * v.x + y * v.y + z * v.z; }
    
    Vec3 cross(const Vec3& v) const {
        return Vec3(
            y * v.z - z * v.y,
            z * v.x - x * v.z,
            x * v.y - y * v.x
        );
    }
    
    // Component-wise operations
    Vec3 abs() const { return Vec3(std::abs(x), std::abs(y), std::abs(z)); }
    Vec3 min(const Vec3& v) const { return Vec3(std::min(x, v.x), std::min(y, v.y), std::min(z, v.z)); }
    Vec3 max(const Vec3& v) const { return Vec3(std::max(x, v.x), std::max(y, v.y), std::max(z, v.z)); }
    
    float minComponent() const { return std::min({x, y, z}); }
    float maxComponent() const { return std::max({x, y, z}); }
    
    // Utility functions
    bool isZero() const { return x == 0 && y == 0 && z == 0; }
    bool hasNaN() const { return std::isnan(x) || std::isnan(y) || std::isnan(z); }
    bool hasInf() const { return std::isinf(x) || std::isinf(y) || std::isinf(z); }

    /**
     * @brief Calculate variance of components (for Color3 usage)
     */
    float variance() const {
        float mean = (x + y + z) / 3.0f;
        float dx = x - mean;
        float dy = y - mean;
        float dz = z - mean;
        return (dx * dx + dy * dy + dz * dz) / 3.0f;
    }
    
    // Static utility functions
    static Vec3 lerp(const Vec3& a, const Vec3& b, float t) {
        return a + (b - a) * t;
    }
    
    static Vec3 reflect(const Vec3& incident, const Vec3& normal) {
        return incident - normal * (2.0f * incident.dot(normal));
    }
    
    static Vec3 refract(const Vec3& incident, const Vec3& normal, float eta) {
        float cosI = -incident.dot(normal);
        float sinT2 = eta * eta * (1.0f - cosI * cosI);
        if (sinT2 >= 1.0f) return Vec3(0); // Total internal reflection
        float cosT = std::sqrt(1.0f - sinT2);
        return incident * eta + normal * (eta * cosI - cosT);
    }
};

// Non-member operators
inline Vec3 operator*(float t, const Vec3& v) { return v * t; }

// Stream operators
inline std::ostream& operator<<(std::ostream& os, const Vec3& v) {
    return os << "Vec3(" << v.x << ", " << v.y << ", " << v.z << ")";
}

// Type aliases
using Point3 = Vec3;  // 3D point
using Color3 = Vec3;  // RGB color
using Normal3 = Vec3; // Surface normal

// Common constants
namespace constants {
    const Vec3 ZERO(0, 0, 0);
    const Vec3 ONE(1, 1, 1);
    const Vec3 X_AXIS(1, 0, 0);
    const Vec3 Y_AXIS(0, 1, 0);
    const Vec3 Z_AXIS(0, 0, 1);
}

} // namespace photon
