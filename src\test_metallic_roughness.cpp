// src/test_metallic_roughness.cpp
// PhotonRender - Metallic Roughness Workflow Test

#include "core/material/material.hpp"
#include "core/material/disney_brdf.hpp"
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/scene/scene.hpp"
#include "core/sampler/random_sampler.hpp"
#include <iostream>
#include <memory>

using namespace photon;

/**
 * @brief Test basic texture functionality
 */
bool testTextureBasics() {
    std::cout << "\n=== Testing Texture Basics ===" << std::endl;
    
    try {
        // Test solid color texture
        auto colorTexture = ImageTexture::createSolid(Color3(0.8f, 0.6f, 0.4f));
        Vec2 uv(0.5f, 0.5f);
        Color3 sampledColor = colorTexture->sample(uv);
        
        std::cout << "✅ Solid color texture: (" << sampledColor.r << ", " 
                  << sampledColor.g << ", " << sampledColor.b << ")" << std::endl;
        
        // Test solid value texture
        auto valueTexture = ImageTexture::createSolid(0.7f);
        float sampledValue = valueTexture->sampleFloat(uv);
        
        std::cout << "✅ Solid value texture: " << sampledValue << std::endl;
        
        // Test checkerboard texture
        CheckerboardTexture checkerboard(Color3(1.0f), Color3(0.0f), 4.0f);
        Color3 checkerColor1 = checkerboard.sample(Vec2(0.1f, 0.1f));
        Color3 checkerColor2 = checkerboard.sample(Vec2(0.6f, 0.6f));
        
        std::cout << "✅ Checkerboard texture: (" << checkerColor1.r << ", " 
                  << checkerColor1.g << ", " << checkerColor1.b << ") vs (" 
                  << checkerColor2.r << ", " << checkerColor2.g << ", " << checkerColor2.b << ")" << std::endl;
        
        // Test noise texture
        NoiseTexture noise(4.0f, 1.0f, 3);
        Color3 noiseColor = noise.sample(Vec2(0.3f, 0.7f));
        
        std::cout << "✅ Noise texture: (" << noiseColor.r << ", " 
                  << noiseColor.g << ", " << noiseColor.b << ")" << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Texture test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test PBR material with textures
 */
bool testPBRWithTextures() {
    std::cout << "\n=== Testing PBR Material with Textures ===" << std::endl;
    
    try {
        // Create PBR material
        auto material = PBRMaterial::createPreset("metal", Color3(0.8f, 0.6f, 0.4f));
        
        // Create textures
        auto baseColorTexture = ImageTexture::createSolid(Color3(0.9f, 0.7f, 0.5f));
        auto metallicTexture = ImageTexture::createSolid(0.8f);
        auto roughnessTexture = ImageTexture::createSolid(0.3f);
        
        // Assign textures
        material->setBaseColorTexture(baseColorTexture);
        material->setMetallicTexture(metallicTexture);
        material->setRoughnessTexture(roughnessTexture);
        
        std::cout << "✅ Textures assigned to PBR material" << std::endl;
        
        // Test material evaluation with textures
        Intersection isect;
        isect.hit = true;
        isect.n = Normal3(0, 0, 1);
        isect.p = Point3(0, 0, 0);
        isect.u = 0.5f;
        isect.v = 0.5f;
        isect.dpdu = Vec3(1, 0, 0);
        isect.dpdv = Vec3(0, 1, 0);
        
        Vec3 wo(0, 0, 1);
        Vec3 wi(0.5f, 0.5f, 0.707f);
        wi = wi.normalized();
        
        Color3 f = material->f(isect, wo, wi);
        std::cout << "✅ Material evaluation with textures: f = (" 
                  << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
        
        // Test material sampling with textures
        RandomSampler sampler;
        BSDFSample sample = material->sample(isect, wo, sampler);
        
        if (sample.isValid()) {
            std::cout << "✅ Material sampling with textures: f = (" 
                      << sample.f.r << ", " << sample.f.g << ", " << sample.f.b 
                      << "), pdf = " << sample.pdf << std::endl;
        } else {
            std::cout << "❌ Invalid material sample" << std::endl;
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ PBR texture test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test metallic roughness workflow variations
 */
bool testMetallicRoughnessVariations() {
    std::cout << "\n=== Testing Metallic Roughness Variations ===" << std::endl;
    
    try {
        // Test different metallic/roughness combinations
        std::vector<std::pair<float, float>> variations = {
            {0.0f, 0.1f},  // Smooth dielectric
            {0.0f, 0.9f},  // Rough dielectric
            {1.0f, 0.1f},  // Smooth metal
            {1.0f, 0.9f},  // Rough metal
            {0.5f, 0.5f}   // Mixed
        };
        
        for (const auto& [metallic, roughness] : variations) {
            // Create material with specific parameters
            auto material = std::make_shared<PBRMaterial>(Color3(0.8f, 0.6f, 0.4f), metallic, roughness);
            
            // Create textures that modify the base values
            auto metallicTexture = ImageTexture::createSolid(metallic);
            auto roughnessTexture = ImageTexture::createSolid(roughness);
            
            material->setMetallicTexture(metallicTexture);
            material->setRoughnessTexture(roughnessTexture);
            
            // Test energy conservation
            bool energyOK = material->validateEnergyConservation();
            
            std::cout << "✅ Metallic=" << metallic << ", Roughness=" << roughness 
                      << ", Energy=" << (energyOK ? "OK" : "FAIL") << std::endl;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Metallic roughness variations test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test texture filtering and wrapping
 */
bool testTextureFiltering() {
    std::cout << "\n=== Testing Texture Filtering and Wrapping ===" << std::endl;
    
    try {
        // Create checkerboard texture for testing
        CheckerboardTexture checkerboard(Color3(1.0f), Color3(0.0f), 2.0f);
        
        // Test different wrap modes
        checkerboard.setWrap(TextureWrap::REPEAT);
        Color3 repeatColor = checkerboard.sample(Vec2(1.5f, 1.5f));
        
        checkerboard.setWrap(TextureWrap::CLAMP);
        Color3 clampColor = checkerboard.sample(Vec2(1.5f, 1.5f));
        
        checkerboard.setWrap(TextureWrap::MIRROR);
        Color3 mirrorColor = checkerboard.sample(Vec2(1.5f, 1.5f));
        
        std::cout << "✅ Wrap modes - Repeat: (" << repeatColor.r << ", " << repeatColor.g << ", " << repeatColor.b 
                  << "), Clamp: (" << clampColor.r << ", " << clampColor.g << ", " << clampColor.b 
                  << "), Mirror: (" << mirrorColor.r << ", " << mirrorColor.g << ", " << mirrorColor.b << ")" << std::endl;
        
        // Test different filter modes
        checkerboard.setFilter(TextureFilter::NEAREST);
        Color3 nearestColor = checkerboard.sample(Vec2(0.25f, 0.25f));
        
        checkerboard.setFilter(TextureFilter::BILINEAR);
        Color3 bilinearColor = checkerboard.sample(Vec2(0.25f, 0.25f));
        
        std::cout << "✅ Filter modes - Nearest: (" << nearestColor.r << ", " << nearestColor.g << ", " << nearestColor.b 
                  << "), Bilinear: (" << bilinearColor.r << ", " << bilinearColor.g << ", " << bilinearColor.b << ")" << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Texture filtering test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test complete metallic roughness workflow
 */
bool testCompleteWorkflow() {
    std::cout << "\n=== Testing Complete Metallic Roughness Workflow ===" << std::endl;
    
    try {
        // Create a complete PBR material setup
        auto material = PBRMaterial::createPreset("plastic", Color3(0.2f, 0.4f, 0.8f));
        
        // Create varied textures
        auto baseColorTexture = std::make_shared<CheckerboardTexture>(
            Color3(0.8f, 0.2f, 0.2f), Color3(0.2f, 0.8f, 0.2f), 4.0f);
        auto metallicTexture = std::make_shared<CheckerboardTexture>(
            Color3(0.0f), Color3(1.0f), 8.0f);
        auto roughnessTexture = std::make_shared<NoiseTexture>(2.0f, 0.5f, 3);
        
        // Assign all textures
        material->setBaseColorTexture(baseColorTexture);
        material->setMetallicTexture(metallicTexture);
        material->setRoughnessTexture(roughnessTexture);
        
        // Test at different UV coordinates
        std::vector<Vec2> testUVs = {
            Vec2(0.1f, 0.1f),
            Vec2(0.3f, 0.7f),
            Vec2(0.6f, 0.2f),
            Vec2(0.9f, 0.9f)
        };
        
        for (const auto& uv : testUVs) {
            Intersection isect;
            isect.hit = true;
            isect.n = Normal3(0, 0, 1);
            isect.p = Point3(0, 0, 0);
            isect.u = uv.x;
            isect.v = uv.y;
            isect.dpdu = Vec3(1, 0, 0);
            isect.dpdv = Vec3(0, 1, 0);
            
            Vec3 wo(0, 0, 1);
            Vec3 wi(0.3f, 0.3f, 0.9f);
            wi = wi.normalized();
            
            Color3 f = material->f(isect, wo, wi);
            
            std::cout << "✅ UV(" << uv.x << ", " << uv.y << ") -> f = (" 
                      << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Complete workflow test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Main test function
 */
int main() {
    std::cout << "PhotonRender Metallic Roughness Workflow Test" << std::endl;
    std::cout << "==============================================" << std::endl;
    
    bool allPassed = true;
    
    // Run all tests
    allPassed &= testTextureBasics();
    allPassed &= testPBRWithTextures();
    allPassed &= testMetallicRoughnessVariations();
    allPassed &= testTextureFiltering();
    allPassed &= testCompleteWorkflow();
    
    std::cout << "\n=== Test Results ===" << std::endl;
    if (allPassed) {
        std::cout << "🎉 All Metallic Roughness Workflow tests PASSED!" << std::endl;
        std::cout << "✅ Texture system is working correctly!" << std::endl;
        std::cout << "✅ PBR material texture integration is complete!" << std::endl;
    } else {
        std::cout << "❌ Some Metallic Roughness Workflow tests FAILED!" << std::endl;
    }
    
    return allPassed ? 0 : 1;
}
