@echo off
REM build_pbr_validation.bat
REM Build script for PBR Validation Tests - Phase 3.2.1 Completion
REM Path: build_pbr_validation.bat

echo ========================================
echo PhotonRender PBR Validation Build
echo Phase 3.2.1 Completion Test
echo ========================================

REM Set build directory
set BUILD_DIR=build_validation
if not exist %BUILD_DIR% mkdir %BUILD_DIR%

echo.
echo 🔧 Building PBR Validation Test...

REM Setup Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" > nul 2>&1

REM Simple compilation using MSVC
cl /std:c++17 ^
    /I src ^
    /I include ^
    /O2 ^
    /DNDEBUG ^
    /EHsc ^
    src/test_pbr_validation.cpp ^
    src/core/material/disney_brdf.cpp ^
    src/core/material/material.cpp ^
    src/core/texture/texture.cpp ^
    src/core/math/vec3.cpp ^
    src/core/math/vec2.cpp ^
    src/core/sampler/sampler.cpp ^
    src/core/scene/scene.cpp ^
    src/core/image/image.cpp ^
    /Fe:%BUILD_DIR%/test_pbr_validation.exe

if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.

echo 🚀 Running PBR Validation Tests...
echo.

REM Run the validation tests
%BUILD_DIR%/test_pbr_validation.exe

if %ERRORLEVEL% equ 0 (
    echo.
    echo 🎉 PBR Validation COMPLETED SUCCESSFULLY!
    echo 🎊 Phase 3.2.1 Disney PBR Materials System - 100% COMPLETE!
    echo 🚀 Ready for Phase 3.2.2 Advanced Lighting System!
) else (
    echo.
    echo ❌ Some validation tests failed.
    echo Please check the output above for details.
)

echo.
echo Press any key to continue...
pause > nul
