// src/test_subsurface_scattering.cpp
// PhotonRender - Subsurface Scattering Test

#include "core/material/disney_brdf.hpp"
#include "core/material/material.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/scene/scene.hpp"
#include <iostream>
#include <vector>
#include <memory>

using namespace photon;

/**
 * @brief Test basic subsurface scattering functionality
 */
bool testSubsurfaceBasics() {
    std::cout << "\n=== Testing Subsurface Scattering Basics ===" << std::endl;
    
    try {
        // Create Disney BRDF with subsurface scattering
        DisneyBRDFParams params;
        params.baseColor = Color3(0.8f, 0.6f, 0.5f);
        params.metallic = 0.0f;
        params.roughness = 0.4f;
        params.subsurface = 0.8f;
        params.validate();
        
        DisneyBRDF brdf(params);
        std::cout << "✅ Disney BRDF with subsurface created: subsurface=" << params.subsurface << std::endl;
        
        // Test BRDF evaluation with subsurface
        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);
        Vec3 wi(0.3f, 0.3f, 0.9f);
        wi = wi.normalized();
        
        Color3 f = brdf.eval(wo, wi, n);
        std::cout << "✅ BRDF evaluation with subsurface: f = (" 
                  << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
        
        // Test BRDF sampling with subsurface
        RandomSampler sampler;
        Vec3 sampledWi;
        float pdf;
        Color3 sampledF = brdf.sample(wo, n, sampler, sampledWi, pdf);
        
        std::cout << "✅ BRDF sampling with subsurface: f = (" 
                  << sampledF.r << ", " << sampledF.g << ", " << sampledF.b 
                  << "), pdf = " << pdf << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Subsurface basics test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test subsurface material presets
 */
bool testSubsurfacePresets() {
    std::cout << "\n=== Testing Subsurface Material Presets ===" << std::endl;
    
    std::vector<std::pair<std::string, std::function<DisneyBRDFParams(const Color3&)>>> presets = {
        {"skin", DisneyMaterialPresets::createSkin},
        {"wax", DisneyMaterialPresets::createWax},
        {"marble", DisneyMaterialPresets::createMarble},
        {"jade", DisneyMaterialPresets::createJade}
    };
    
    for (const auto& [name, createFunc] : presets) {
        try {
            DisneyBRDFParams params = createFunc(Color3(0.8f, 0.6f, 0.4f));
            
            // Test energy conservation
            bool energyOK = DisneyMaterialPresets::validateEnergyConservation(params);
            
            std::cout << "✅ " << name << " preset: subsurface=" << params.subsurface 
                      << ", metallic=" << params.metallic << ", roughness=" << params.roughness 
                      << ", energy=" << (energyOK ? "OK" : "FAIL") << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "❌ " << name << " preset failed: " << e.what() << std::endl;
            return false;
        }
    }
    
    return true;
}

/**
 * @brief Test subsurface vs regular diffuse comparison
 */
bool testSubsurfaceComparison() {
    std::cout << "\n=== Testing Subsurface vs Regular Diffuse ===" << std::endl;
    
    try {
        Color3 baseColor(0.8f, 0.6f, 0.5f);
        
        // Create regular diffuse material
        DisneyBRDFParams diffuseParams;
        diffuseParams.baseColor = baseColor;
        diffuseParams.metallic = 0.0f;
        diffuseParams.roughness = 0.4f;
        diffuseParams.subsurface = 0.0f;
        DisneyBRDF diffuseBRDF(diffuseParams);
        
        // Create subsurface material
        DisneyBRDFParams subsurfaceParams;
        subsurfaceParams.baseColor = baseColor;
        subsurfaceParams.metallic = 0.0f;
        subsurfaceParams.roughness = 0.4f;
        subsurfaceParams.subsurface = 0.8f;
        DisneyBRDF subsurfaceBRDF(subsurfaceParams);
        
        // Test at different angles
        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);
        
        std::vector<Vec3> testDirections = {
            Vec3(0, 0, 1).normalized(),      // Normal incidence
            Vec3(0.3f, 0, 0.95f).normalized(), // Slight angle
            Vec3(0.7f, 0, 0.7f).normalized(),  // 45 degrees
            Vec3(0.9f, 0, 0.44f).normalized()  // Grazing angle
        };
        
        for (const auto& wi : testDirections) {
            Color3 diffuseF = diffuseBRDF.eval(wo, wi, n);
            Color3 subsurfaceF = subsurfaceBRDF.eval(wo, wi, n);
            
            float angle = std::acos(wi.dot(n)) * 180.0f / 3.14159f;
            
            std::cout << "✅ Angle " << angle << "°: Diffuse=(" 
                      << diffuseF.r << ", " << diffuseF.g << ", " << diffuseF.b 
                      << "), Subsurface=(" << subsurfaceF.r << ", " << subsurfaceF.g 
                      << ", " << subsurfaceF.b << ")" << std::endl;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Subsurface comparison test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test subsurface energy conservation
 */
bool testSubsurfaceEnergyConservation() {
    std::cout << "\n=== Testing Subsurface Energy Conservation ===" << std::endl;
    
    try {
        // Test different subsurface values
        std::vector<float> subsurfaceValues = {0.0f, 0.2f, 0.5f, 0.8f, 1.0f};
        
        for (float subsurfaceValue : subsurfaceValues) {
            DisneyBRDFParams params;
            params.baseColor = Color3(0.8f, 0.6f, 0.4f);
            params.metallic = 0.0f;
            params.roughness = 0.4f;
            params.subsurface = subsurfaceValue;
            
            DisneyBRDF brdf(params);
            RandomSampler sampler;
            
            // Monte Carlo integration for energy conservation
            Vec3 n(0, 0, 1);
            Vec3 wo(0, 0, 1);
            
            const int numSamples = 1000;
            Color3 totalReflectance(0.0f);
            int validSamples = 0;
            
            for (int i = 0; i < numSamples; ++i) {
                Vec3 wi;
                float pdf;
                Color3 f = brdf.sample(wo, n, sampler, wi, pdf);
                
                if (pdf > 0.0f && wi.dot(n) > 0.0f) {
                    totalReflectance = totalReflectance + f * (wi.dot(n) / pdf);
                    validSamples++;
                }
            }
            
            if (validSamples > 0) {
                totalReflectance = totalReflectance / float(validSamples);
                float maxReflectance = std::max({totalReflectance.r, totalReflectance.g, totalReflectance.b});
                
                bool energyOK = maxReflectance <= 1.1f; // Allow some Monte Carlo error
                std::cout << "✅ Subsurface=" << subsurfaceValue << ": Max reflectance=" 
                          << maxReflectance << " (" << (energyOK ? "OK" : "FAIL") 
                          << "), Valid samples: " << validSamples << "/" << numSamples << std::endl;
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Subsurface energy conservation test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test PBR material with subsurface
 */
bool testPBRMaterialSubsurface() {
    std::cout << "\n=== Testing PBR Material with Subsurface ===" << std::endl;
    
    try {
        // Create PBR material with subsurface
        auto material = PBRMaterial::createPreset("skin", Color3(0.8f, 0.6f, 0.5f));
        
        // Test material evaluation
        Intersection isect;
        isect.hit = true;
        isect.n = Normal3(0, 0, 1);
        isect.p = Point3(0, 0, 0);
        isect.u = 0.5f;
        isect.v = 0.5f;
        
        Vec3 wo(0, 0, 1);
        Vec3 wi(0.3f, 0.3f, 0.9f);
        wi = wi.normalized();
        
        Color3 f = material->f(isect, wo, wi);
        std::cout << "✅ PBR Material subsurface evaluation: f = (" 
                  << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
        
        // Test material sampling
        RandomSampler sampler;
        BSDFSample sample = material->sample(isect, wo, sampler);
        
        if (sample.isValid()) {
            std::cout << "✅ PBR Material subsurface sampling: f = (" 
                      << sample.f.r << ", " << sample.f.g << ", " << sample.f.b 
                      << "), pdf = " << sample.pdf << std::endl;
        } else {
            std::cout << "❌ Invalid PBR material sample" << std::endl;
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ PBR material subsurface test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Main test function
 */
int main() {
    std::cout << "PhotonRender Subsurface Scattering Test" << std::endl;
    std::cout << "=======================================" << std::endl;
    
    bool allPassed = true;
    
    // Run all tests
    allPassed &= testSubsurfaceBasics();
    allPassed &= testSubsurfacePresets();
    allPassed &= testSubsurfaceComparison();
    allPassed &= testSubsurfaceEnergyConservation();
    allPassed &= testPBRMaterialSubsurface();
    
    std::cout << "\n=== Test Results ===" << std::endl;
    if (allPassed) {
        std::cout << "🎉 All Subsurface Scattering tests PASSED!" << std::endl;
        std::cout << "✅ Subsurface scattering implementation is working correctly!" << std::endl;
        std::cout << "✅ Disney BRDF now supports translucent materials!" << std::endl;
    } else {
        std::cout << "❌ Some Subsurface Scattering tests FAILED!" << std::endl;
    }
    
    return allPassed ? 0 : 1;
}
