# PhotonRender - Phase 3.2 Technical Specification
**Data Creazione**: 2025-06-20  
**Fase**: 3.2 Advanced Rendering  
**Status**: 🚀 Active Development  
**Timeline**: 3 settimane (Settimane 5-7)

## 🎯 Obiettivo Tecnico

Implementare sistema di rendering fotorealistico avanzato che trasformi PhotonRender da motore di test a renderer professionale comparabile a soluzioni commerciali.

## 📋 Architettura Tecnica

### 🎨 **Fase 3.2.1: Disney PBR Materials System**

#### **Disney Principled BRDF Implementation**
```cpp
class DisneyBRDF {
    // Core parameters
    Color3 baseColor;        // Albedo/diffuse color
    float metallic;          // 0=dielectric, 1=metallic
    float roughness;         // Surface roughness [0,1]
    float specular;          // Specular reflection strength
    float specularTint;      // Specular color tint
    float anisotropic;       // Anisotropic reflection
    float sheen;             // Fabric-like sheen
    float sheenTint;         // Sheen color tint
    float clearcoat;         // Clear coat layer
    float clearcoatGloss;    // Clear coat roughness
    float subsurface;        // Subsurface scattering
    
    // BRDF evaluation
    Color3 eval(const Vec3& wi, const Vec3& wo, const Vec3& n) const;
    Color3 sample(const Vec3& wo, const Vec3& n, Sampler& sampler) const;
    float pdf(const Vec3& wi, const Vec3& wo, const Vec3& n) const;
};
```

#### **Fresnel Calculations**
- **Schlick Approximation**: Per dielettrici (vetro, plastica)
- **Conductor Fresnel**: Per metalli con IOR complesso
- **Energy Conservation**: Bilanciamento diffuse/specular

#### **PBR Material Class**
```cpp
class PBRMaterial : public Material {
    DisneyBRDF brdf;
    std::shared_ptr<Texture> baseColorTexture;
    std::shared_ptr<Texture> metallicTexture;
    std::shared_ptr<Texture> roughnessTexture;
    std::shared_ptr<Texture> normalTexture;
    
    // SketchUp integration
    void fromSketchUpMaterial(const SketchUpMaterial& material);
    Json::Value toJSON() const;
    void fromJSON(const Json::Value& json);
};
```

### ⚡ **Fase 3.2.2: Advanced Lighting System**

#### **HDRI Environment Lighting**
```cpp
class HDRIEnvironment {
    std::shared_ptr<HDRTexture> environmentMap;
    std::shared_ptr<ImportanceSampler> sampler;
    
    // Importance sampling setup
    void buildImportanceTable();
    Vec3 sampleDirection(const Vec2& u, float& pdf) const;
    Color3 evaluate(const Vec3& direction) const;
};
```

#### **Area Lights Implementation**
```cpp
class AreaLight : public Light {
    enum Shape { RECTANGLE, DISK, SPHERE };
    Shape shape;
    Vec2 size;           // For rectangle
    float radius;        // For disk/sphere
    Color3 emission;
    float intensity;
    
    // Importance sampling
    Vec3 samplePoint(const Vec2& u, float& pdf) const;
    Color3 evaluateEmission(const Vec3& point, const Vec3& normal) const;
};
```

#### **Multiple Importance Sampling (MIS)**
```cpp
class MISIntegrator : public PathTracingIntegrator {
    Color3 estimateDirectLighting(const Intersection& hit, 
                                 const Scene& scene, 
                                 Sampler& sampler) const {
        Color3 result(0.0f);
        
        // BSDF sampling
        Color3 bsdfSample = sampleBSDF(hit, sampler);
        float bsdfPdf = getBSDFPdf(hit);
        float lightPdf = getLightPdf(hit);
        float bsdfWeight = powerHeuristic(bsdfPdf, lightPdf);
        result += bsdfWeight * bsdfSample;
        
        // Light sampling  
        Color3 lightSample = sampleLight(hit, scene, sampler);
        lightPdf = getLightPdf(hit);
        bsdfPdf = getBSDFPdf(hit);
        float lightWeight = powerHeuristic(lightPdf, bsdfPdf);
        result += lightWeight * lightSample;
        
        return result;
    }
};
```

### 🖼️ **Fase 3.2.3: Texture System Enhancement**

#### **UV Mapping System**
```cpp
class UVMapper {
    struct UVSet {
        std::vector<Vec2> uvCoords;
        std::string name;
    };
    
    std::vector<UVSet> uvSets;
    
    // SketchUp UV extraction
    void extractFromSketchUp(const SketchUpFace& face);
    Vec2 getUV(int vertexIndex, int uvSet = 0) const;
};
```

#### **Texture Loading Pipeline**
```cpp
class TextureManager {
    std::unordered_map<std::string, std::shared_ptr<Texture>> cache;
    size_t maxMemoryUsage;
    
    std::shared_ptr<Texture> loadTexture(const std::string& path);
    void compressTexture(Texture& texture);
    void streamTexture(const std::string& path);
};
```

#### **Procedural Textures**
```cpp
class ProceduralTexture : public Texture {
    enum Type { NOISE, CHECKERBOARD, GRADIENT, BRICK };
    Type type;
    
    virtual Color3 sample(const Vec2& uv) const = 0;
};

class NoiseTexture : public ProceduralTexture {
    float frequency, amplitude;
    int octaves;
    
    Color3 sample(const Vec2& uv) const override {
        return Color3(perlinNoise(uv * frequency));
    }
};
```

### 🎛️ **Fase 3.2.4: Material Editor Interface**

#### **Real-time Preview System**
```cpp
class MaterialPreview {
    std::shared_ptr<Scene> previewScene;
    std::shared_ptr<Renderer> previewRenderer;
    
    void updateMaterial(const PBRMaterial& material);
    void renderPreview(int width, int height);
    void setPreviewGeometry(PreviewShape shape);
};
```

#### **Material Editor UI (HTML/JavaScript)**
```html
<div class="material-editor">
    <div class="preview-panel">
        <canvas id="material-preview"></canvas>
    </div>
    <div class="parameter-panel">
        <div class="parameter-group">
            <label>Base Color</label>
            <input type="color" id="baseColor">
            <input type="range" id="metallic" min="0" max="1" step="0.01">
            <input type="range" id="roughness" min="0" max="1" step="0.01">
        </div>
    </div>
</div>
```

## 📊 Performance Targets

### **Rendering Performance**
- **PBR Materials**: <2x slowdown vs current diffuse
- **HDRI Lighting**: <3x slowdown vs point lights
- **Texture Sampling**: <1.5x slowdown vs solid colors
- **Overall Target**: <5x slowdown per advanced features

### **Memory Usage**
- **Texture Cache**: <2GB per scene
- **HDRI Maps**: <500MB per environment
- **Material Data**: <100MB per 1000 materials
- **Total Budget**: <4GB VRAM per complex scene

### **Quality Metrics**
- **Energy Conservation**: <1% error in BRDF validation
- **Noise Convergence**: 50% faster with MIS vs naive sampling
- **Visual Accuracy**: Match reference renders within 5% RMSE

## 🔧 Implementation Strategy

### **Week 5: Disney PBR Foundation**
1. **Day 1-2**: Disney BRDF core implementation
2. **Day 3-4**: Fresnel calculations and validation
3. **Day 5-7**: PBR Material class and SketchUp integration

### **Week 6: Advanced Lighting**
1. **Day 1-2**: HDRI environment loading and sampling
2. **Day 3-4**: Area lights implementation
3. **Day 5-7**: MIS integration and optimization

### **Week 7: Textures & UI**
1. **Day 1-3**: Texture system and UV mapping
2. **Day 4-5**: Material editor interface
3. **Day 6-7**: Integration testing and polish

## 🧪 Testing & Validation

### **Unit Tests**
- BRDF energy conservation
- Fresnel accuracy vs reference
- Texture sampling correctness
- UV mapping validation

### **Integration Tests**
- End-to-end PBR rendering
- HDRI environment accuracy
- Material editor workflow
- Performance benchmarks

### **Visual Tests**
- Cornell Box with PBR materials
- HDRI lighting comparison
- Texture detail validation
- Material library showcase

---

**Status**: 🚀 **READY FOR IMPLEMENTATION**  
**Next Action**: Start Disney BRDF Core Implementation  
**Success Criteria**: Photorealistic rendering comparable to commercial solutions
